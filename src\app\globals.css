@import url("https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&display=swap");
@import "tailwindcss";

body {
  direction: rtl;
}

* {
  font-family: "Cairo", sans-serif;
}

html {
  scroll-behavior: smooth;
}

/* Custom Scrollbar Styles */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

/* Animation Delays */
.animation-delay-75 {
  animation-delay: 75ms;
}

.animation-delay-100 {
  animation-delay: 100ms;
}

.animation-delay-150 {
  animation-delay: 150ms;
}

.animation-delay-200 {
  animation-delay: 200ms;
}

.animation-delay-300 {
  animation-delay: 300ms;
}

/* Shimmer Animation */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite linear;
}


.videoPlayerHeight {
  height: calc(100% - 65px);
}

@layer utilities {
  .max-container {
    @apply mx-auto max-w-[1440px];
  }

  .padding-container {
    @apply px-6 lg:px-20;
  }

  .flexCenter {
    @apply flex items-center justify-center;
  }

  .flexBetween {
    @apply flex items-center justify-between;
  }

  /* FONTS */
  .regular-18 {
    @apply text-[18px] font-[400];
  }

  .regular-16 {
    @apply text-[16px] font-[400];
  }

  .regular-14 {
    @apply text-[14px] font-[400];
  }

  .regular-12 {
    @apply text-[12px] font-[400];
  }

  .bold-64 {
    @apply text-[64px] font-[700] leading-[150%];
  }

  .bold-40 {
    @apply text-[40px] font-[700] leading-[120%];
  }

  .bold-32 {
    @apply text-[32px] font-[700] leading-[120%];
  }

  .bold-20 {
    @apply text-[20px] font-[700];
  }

  .bold-18 {
    @apply text-[18px] font-[700];
  }

  .bold-16 {
    @apply text-[16px] font-[700];
  }

  .bold-14 {
    @apply text-[14px] font-[700];
  }

  .bg-main {
    @apply bg-[#f8f9fa];
  }

  .bg-secondary {
    @apply bg-[#87ceeb];
  }

  .bg-accent {
    @apply bg-[#088395];
  }

  .text-main {
    @apply text-[#f8f9fa];
  }

  .text-secondary {
    @apply text-[#87ceeb];
  }

  .text-accent {
    @apply text-[#088395];
  }

  .border-secondary {
    @apply border-[#87ceeb];
  }

  .font-main {
    @apply font-[Cairo];
  }
}