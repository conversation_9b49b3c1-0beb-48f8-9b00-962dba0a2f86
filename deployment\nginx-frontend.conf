# Nginx Configuration for Academitna Frontend
# ===========================================
# Place this configuration in the main nginx-academitna.conf file
# This is the frontend-specific part of the configuration

# Frontend serving configuration (add this to the main server block)

# Root directory for frontend files
root /var/www/academitna;
index index.html index.htm;

# Gzip compression for frontend assets
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_comp_level 6;
gzip_types
    text/plain
    text/css
    text/xml
    text/javascript
    application/javascript
    application/xml+rss
    application/json
    application/xml
    image/svg+xml
    font/woff
    font/woff2;

# Security headers for frontend
add_header X-Frame-Options DENY always;
add_header X-Content-Type-Options nosniff always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;

# Content Security Policy
add_header Content-Security-Policy "
    default-src 'self';
    script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com;
    style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net;
    font-src 'self' https://fonts.gstatic.com;
    img-src 'self' data: https: blob: https://academitna.b-cdn.net;
    media-src 'self' https: blob: https://academitna.b-cdn.net;
    connect-src 'self' https://academitna.online wss://academitna.online;
    frame-src 'self' https://iframe.mediadelivery.net;
    object-src 'none';
    base-uri 'self';
    form-action 'self';
    frame-ancestors 'none';
    upgrade-insecure-requests;
" always;

# Main frontend routes
location / {
    try_files $uri $uri/ /index.html;
    
    # Cache control for HTML files
    location ~* \.html$ {
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
}

# Next.js specific routes (if using Next.js)
location /_next/static/ {
    alias /var/www/academitna/_next/static/;
    expires 1y;
    add_header Cache-Control "public, immutable";
    
    # Compress static assets
    gzip_static on;
}

location /_next/image {
    proxy_pass http://localhost:3000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

# Static assets caching
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp|avif)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    
    # Enable gzip for static assets
    gzip_static on;
    
    # CORS headers for fonts and assets
    location ~* \.(woff|woff2|ttf|eot)$ {
        add_header Access-Control-Allow-Origin "*";
    }
}

# Favicon and manifest files
location ~* \.(ico|manifest\.json|browserconfig\.xml)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    log_not_found off;
}

# Robots.txt and sitemap
location = /robots.txt {
    allow all;
    log_not_found off;
    access_log off;
}

location = /sitemap.xml {
    allow all;
    log_not_found off;
    access_log off;
}

# Service worker
location = /sw.js {
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
    add_header Expires "0";
}

# PWA manifest
location = /manifest.json {
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Content-Type "application/json";
}

# Health check for frontend
location = /health {
    access_log off;
    return 200 "healthy\n";
    add_header Content-Type text/plain;
}

# Block access to sensitive files
location ~ /\. {
    deny all;
    access_log off;
    log_not_found off;
}

location ~ ~$ {
    deny all;
    access_log off;
    log_not_found off;
}

location ~* \.(env|log|htaccess|htpasswd|ini|phps|fla|psd|log|sh)$ {
    deny all;
    access_log off;
    log_not_found off;
}

# Block common exploit attempts
location ~* (wp-admin|wp-login|xmlrpc|wp-config|wp-content) {
    return 444;
}

location ~* \.(aspx|jsp|cgi)$ {
    return 444;
}

# Rate limiting for specific routes
location /contact {
    limit_req zone=api burst=5 nodelay;
    try_files $uri $uri/ /index.html;
}

location /signup {
    limit_req zone=auth burst=3 nodelay;
    try_files $uri $uri/ /index.html;
}

location /login {
    limit_req zone=auth burst=5 nodelay;
    try_files $uri $uri/ /index.html;
}

# Custom error pages
error_page 404 /404.html;
error_page 500 502 503 504 /50x.html;

location = /404.html {
    root /var/www/academitna;
    internal;
}

location = /50x.html {
    root /var/www/academitna;
    internal;
}

# Logging
access_log /var/log/nginx/academitna_frontend_access.log;
error_log /var/log/nginx/academitna_frontend_error.log;
